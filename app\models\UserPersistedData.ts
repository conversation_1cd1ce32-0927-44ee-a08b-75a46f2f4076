import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class UserPersistedDataModel {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "userPersistedData";
    static async Get(id: string) {
        return await JC_Get<UserPersistedDataModel>(this.apiRoute, { id }, UserPersistedDataModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<UserPersistedDataModel>(`${this.apiRoute}/getList`, UserPersistedDataModel, paging, {});
    }
    static async Create(data: UserPersistedDataModel) {
        return await JC_Put<UserPersistedDataModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: UserPersistedDataModel[]) {
        return await JC_Put<UserPersistedDataModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: UserPersistedDataModel) {
        return await JC_Post<UserPersistedDataModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: UserPersistedDataModel[]) {
        return await JC_Post<UserPersistedDataModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return await JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId: string;
    Code: string;
    Value: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserPersistedDataModel>) {
        this.Id = JC_Utils.generateGuid();
        this.UserId = "";
        this.Code = "";
        this.Value = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Value}`;
    }
}
