import { NextResponse, NextRequest } from "next/server";
import { unstable_noStore } from "next/cache";
import { PropertyBusiness } from "../business";
import { JC_ListPagingModel } from "@/app/models/ComponentModels/JC_ListPagingModel";

export const dynamic = 'force-dynamic';

// Get all Property
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Parse paging parameters from URL
        const { searchParams } = new URL(request.url);
        let pageSize = searchParams.get('PageSize');
        const paging:JC_ListPagingModel|undefined = !pageSize ? undefined : {
            PageSize: searchParams.get('PageSize') ? parseInt(searchParams.get('PageSize')!) : 50,
            PageIndex: searchParams.get('PageIndex') ? parseInt(searchParams.get('PageIndex')!) : 0,
            SortField: searchParams.get('SortField') || 'SortOrder',
            SortAsc: (searchParams.get('SortDir') || 'asc').toLowerCase() !== 'desc'
        };

        const result = await PropertyBusiness.GetList(paging);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
