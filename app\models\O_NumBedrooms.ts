import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class O_NumBedroomsModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "o_numBedrooms";
    static async Get(code: string) {
        return await JC_Get<O_NumBedroomsModel>(this.apiRoute, { code }, O_NumBedroomsModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<O_NumBedroomsModel>(`${this.apiRoute}/getList`, O_NumBedroomsModel, paging, {});
    }
    static async Create(data: O_NumBedroomsModel) {
        return await JC_Put<O_NumBedroomsModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_NumBedroomsModel[]) {
        return await JC_Put<O_NumBedroomsModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_NumBedroomsModel) {
        return await JC_Post<O_NumBedroomsModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_NumBedroomsModel[]) {
        return await JC_Post<O_NumBedroomsModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_NumBedroomsModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
